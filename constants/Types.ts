export enum Roles {
  user = 'user',
  assistant = 'assistant',
  developer = 'developer',
}

export type ChatMessage = {
  role: Roles;
  content: string;
};

export type DietPreferences = {
  allergies: string[];
  diet: string;
  timeToCook: string;
  experience: ExperienceLevel;
  calories: number;
  goals: string;
};

export enum ExperienceLevel {
  beginner = 'beginner',
  intermediate = 'intermediate',
  experienced = 'experienced',
}

export enum ChatMode {
  chat = 'chat',
  onboarding = 'onboarding',
}

// Special constant for chat messages that should render a camera button
export const SHOW_TAKE_PICTURE_BUTTON = '__SHOW_TAKE_PICTURE_BUTTON__';
