/**
 * JSON schema for inventory categorization
 *
 * This schema defines the expected structure of the response from the LLM API
 * when categorizing inventory items.
 */
export const inventoryCategorizationSchema = {
  type: 'object',
  properties: {
    categories: {
      type: 'array',
      items: {
        type: 'object',
        properties: {
          name: { type: 'string' },
          emoji: { type: 'string' },
          items: {
            type: 'array',
            items: {
              type: 'object',
              properties: {
                name: { type: 'string' },
                quantity: { type: 'number' }
              },
              required: ['name', 'quantity'],
              additionalProperties: false
            }
          }
        },
        required: ['name', 'emoji', 'items'],
        additionalProperties: false
      }
    }
  },
  required: ['categories'],
  additionalProperties: false,
};
