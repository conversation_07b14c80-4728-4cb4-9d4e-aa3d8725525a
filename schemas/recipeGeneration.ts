/**
 * JSON schemas for recipe generation
 *
 * These schemas define the expected structure of the responses from the LLM API
 * when generating recipes based on ingredients and dietary preferences.
 */

/**
 * Schema for basic recipe information (without ingredients and instructions)
 * Used for the initial recipe list display
 */
export const recipeBasicsSchema = {
  type: 'object',
  properties: {
    recipes: {
      type: 'array',
      items: {
        type: 'object',
        properties: {
          id: { type: 'string' },
          title: { type: 'string' },
          timeInMinutes: { type: 'number' },
          calories: { type: 'number' },
          imageQuery: { type: 'string' },
          mealType: {
            type: 'string',
            enum: ['Breakfast', 'Lunch', 'Dinner', 'Snacks', 'Dessert']
          }
        },
        required: ['id', 'title', 'timeInMinutes', 'calories', 'imageQuery', 'mealType'],
        additionalProperties: false
      }
    }
  },
  required: ['recipes'],
  additionalProperties: false
};

/**
 * Schema for detailed recipe information (with ingredients and instructions)
 * Used when a user clicks on a recipe to view details
 */
export const recipeDetailsSchema = {
  type: 'object',
  properties: {
    ingredients: {
      type: 'array',
      items: {
        type: 'object',
        properties: {
          name: { type: 'string' },
          available: { type: 'boolean' }
        },
        required: ['name', 'available'],
        additionalProperties: false
      }
    },
    instructions: {
      type: 'object',
      properties: {
        'High level': { type: 'string' },
        'Detailed': { type: 'string' },
        'Teach mode': { type: 'string' }
      },
      required: ['High level', 'Detailed', 'Teach mode'],
      additionalProperties: false
    }
  },
  required: ['ingredients', 'instructions'],
  additionalProperties: false
};

/**
 * Original full recipe schema (kept for backward compatibility)
 */
export const recipeGenerationSchema = {
  type: 'object',
  properties: {
    recipes: {
      type: 'array',
      items: {
        type: 'object',
        properties: {
          id: { type: 'string' },
          title: { type: 'string' },
          timeInMinutes: { type: 'number' },
          calories: { type: 'number' },
          ingredients: {
            type: 'array',
            items: {
              type: 'object',
              properties: {
                name: { type: 'string' },
                available: { type: 'boolean' }
              },
              required: ['name', 'available'],
              additionalProperties: false
            }
          },
          instructions: {
            type: 'object',
            properties: {
              'High level': { type: 'string' },
              'Detailed': { type: 'string' },
              'Teach mode': { type: 'string' }
            },
            required: ['High level', 'Detailed', 'Teach mode'],
            additionalProperties: false
          },
          imageQuery: { type: 'string' },
          mealType: {
            type: 'string',
            enum: ['Breakfast', 'Lunch', 'Dinner', 'Snacks', 'Dessert']
          }
        },
        required: ['id', 'title', 'timeInMinutes', 'calories', 'ingredients', 'instructions', 'imageQuery', 'mealType'],
        additionalProperties: false
      }
    }
  },
  required: ['recipes'],
  additionalProperties: false
};
