/**
 * JSON schema for diet preferences
 * 
 * This schema defines the expected structure of the response from the LLM API
 * when extracting diet preferences from user conversations.
 */
export const dietPreferencesSchema = {
  type: 'object',
  properties: {
    allergies: {
      type: 'array',
      items: { type: 'string' }
    },
    diet: { type: 'string' },
    timeToCook: { type: 'string' },
    experience: { type: 'string' },
    calories: { type: 'number' },
    goals: { type: 'string' }
  },
  required: ['allergies', 'diet', 'timeToCook', 'experience', 'calories', 'goals'],
  additionalProperties: false,
};
