import { db } from '../firebase/firebaseConfig';
import { collection, getDocs, addDoc, updateDoc, deleteDoc, doc, DocumentData, setDoc, getDoc } from "firebase/firestore";

export enum FirestoreCollections {
  DIET_PREFERENCES = "dietPreferences",
  CONVERSATIONS = "conversations",
  INVENTORY = "inventory",
  GROCERY_LIST = "groceryList",
}

export interface FirestoreRepository {
  getCollection: (collectionName: FirestoreCollections) => Promise<DocumentData[]>;
  getDocument: (collectionName: FirestoreCollections, docId: string) => Promise<DocumentData | null>;
  addDocument: (collectionName: FirestoreCollections, data: DocumentData) => Promise<string>;
  updateDocument: (collectionName: FirestoreCollections, docId: string, data: DocumentData) => Promise<void>;
  deleteDocument: (collectionName: FirestoreCollections, docId: string) => Promise<void>;
  addOrReplaceDocument: (collectionName: string, docId: string, data: object) => Promise<void>;
}

const getCollection = async (collectionName: FirestoreCollections): Promise<DocumentData[]> => {
  const querySnapshot = await getDocs(collection(db, collectionName));
  return querySnapshot.docs.map(doc => ({ id: doc.id, ...doc.data() }));
};

const addDocument = async (collectionName: FirestoreCollections, data: DocumentData): Promise<string> => {
  const docRef = await addDoc(collection(db, collectionName), data);
  return docRef.id;
};

const getDocument = async (collectionName: FirestoreCollections, docId: string): Promise<DocumentData | null> => {
  const docRef = doc(db, collectionName, docId);
  const docSnap = await getDoc(docRef);
  if (docSnap.exists()) {
    return docSnap.data();
  }
  return null;
};

// Function that adds or replaces a document in a specified collection.
// If the document doesn't exist, it creates it. If it does, it overwrites it.
const addOrReplaceDocument = async (collectionName: string, docId: string, data: object): Promise<void> => {
  const docRef = doc(db, collectionName, docId);

  try {
    // Using setDoc without merge will replace the existing document if it exists.
    await setDoc(docRef, data);
    console.log(`Document "${docId}" in collection "${collectionName}" has been successfully added/replaced.`);
  } catch (error) {
    console.error("Error adding/replacing document:", error);
  }
}

const updateDocument = async (collectionName: FirestoreCollections, docId: string, data: DocumentData): Promise<void> => {
  const docRef = doc(db, collectionName, docId);
  await updateDoc(docRef, data);
};

const deleteDocument = async (collectionName: FirestoreCollections, docId: string): Promise<void> => {
  const docRef = doc(db, collectionName, docId);
  await deleteDoc(docRef);
};

export const firestoreRepository: FirestoreRepository = {
  getCollection,
  addDocument,
  getDocument,
  updateDocument,
  deleteDocument,
  addOrReplaceDocument
};
