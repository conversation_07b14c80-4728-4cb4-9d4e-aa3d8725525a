import { useEffect, useState } from 'react';
import { ChatMessage, Roles, DietPreferences, ChatMode, SHOW_TAKE_PICTURE_BUTTON } from '@/constants/Types';
import { firestoreRepository, FirestoreCollections } from '@/repositories/firestoreRepository';
import { ResponseInputItem } from 'openai/resources/responses/responses';
import { useRouter } from 'expo-router';
import { LlmService } from '@/services/LlmService';
import { dietPreferencesSchema } from '@/schemas/dietPreferences';
import { chatCompletionModel } from '@/constants/LlmConfigs';
import { useAuth } from '@/app/contexts/AuthContext';

const SAVE_PREFERENCES = '[SAVE_PREFERENCES]';
const ONBOARDING_COMPLETE = '[ONBOARDING_COMPLETE]';

// Updated onboarding instruction to include the camera step
const onboardingInstruction = `You are a friendly assistant helping users onboard to a recipe app. Your goal is to collect the following information from the user in natual conversation. Ask the questions one by one and keep it concise.
1. Whether the user has any food allergies and if so what they are.
2. Time constraints for cooking (e.g., quick meals, long cooking time, 30 minutes per meal).
3. Cooking experience level (beginner, intermediate, experienced).
4. Daily calorie goal (if any).
5. Other dietary goals or if they are following a specific diet (e.g., keto, paleo, vegan, etc.).
If the user provides incomplete, unclear, or seemingly mistyped answers, follow up with clarifying questions. Limit your conversation to topics related to food, recipes, cooking, and nutrition.
Once you have all the information, summarize their responses in bullet points and append '${SAVE_PREFERENCES}' to your response.`;

const defaultInstruction = `You are a helpful cooking assistant. Answer questions, give meal ideas, nutrition tips, and help users to update their saved preferences.`;

// Special message that will only display a button
const takePictureButtonMessage: ChatMessage = {
  role: Roles.developer,
  content: SHOW_TAKE_PICTURE_BUTTON,
};

const onboardingIntro: ChatMessage = {
  role: Roles.developer,
  content: `Hi there!👋 I'm your Chef Pal, here to help you whip up delicious meals with what you've got in the fridge and pantry. To make sure my recommendations fit your tastes and needs, I just need to ask 6 quick questions. First of all, do you have any food allergies?`,
};

const defaultIntro: ChatMessage = {
  role: Roles.developer,
  content: `Would you like to update any dietary preference? I can also chat about meal ideas and nutrition tips. Here are your current preference settings:`,
};

export function useChatExperience(mode: ChatMode) {
  const isOnboarding = mode === ChatMode.onboarding;
  const { user } = useAuth();
  const [messageInput, setMessageInput] = useState('');
  const [conversation, setConversation] = useState<ChatMessage[]>([isOnboarding ? onboardingIntro : defaultIntro]);
  const [context, setContext] = useState<ResponseInputItem[]>([]);
  const [isTyping, setIsTyping] = useState(isOnboarding ? false : true);
  const [showCameraButton, setShowCameraButton] = useState(false);
  const [preferencesCaptured, setPreferencesCaptured] = useState(false);

  const router = useRouter();

  const systemInstruction = isOnboarding ? onboardingInstruction : defaultInstruction;

  // Add the camera function
  const handleCameraPress = () => {
    if (isOnboarding && preferencesCaptured) {
      const completeMessage = {
        role: Roles.assistant,
        content: `${ONBOARDING_COMPLETE}`,
      };
      setContext((prev) => [...prev, completeMessage]);
      // Now navigate to camera
      router.push('/camera');
    } else {
      router.push('/camera');
    }
  };

  // Fetch diet preferences when user is available and not in onboarding mode
  useEffect(() => {
    if (user && !isOnboarding) {
      fetchAndShowDietPreferences(user.uid);
    }
  }, [user, isOnboarding]);

  useEffect(() => {
    if (conversation.length) {
      storeConversation(conversation);
    }
  }, [conversation]);

  // Add the camera button when needed
  useEffect(() => {
    if (showCameraButton && !conversation.some((msg) => msg.content === takePictureButtonMessage.content)) {
      setConversation((prev) => [...prev, takePictureButtonMessage]);
    }
  }, [showCameraButton]);

  const fetchAndShowDietPreferences = async (userId: string) => {
    try {
      const doc = await firestoreRepository.getDocument(FirestoreCollections.DIET_PREFERENCES, userId);
      if (doc) {
        const preferences = doc as DietPreferences;
        const dietMessage = {
          role: Roles.developer,
          content: `User's dietary preferences are: ${JSON.stringify(preferences)}.`,
        };

        const summaryResponse = await LlmService.callLlmApi(
          chatCompletionModel,
          "Respond ONLY with a bullet points summary of the user's dietary preferences.",
          [dietMessage]
        );

        const outputText = LlmService.extractOutputText(summaryResponse);
        setIsTyping(false);
        setConversation((prev) => [...prev, { role: Roles.assistant, content: outputText }]);
      }
    } catch (error) {
      console.error('Error fetching diet preferences:', error);
    }
  };

  const storeConversation = async (conv: ChatMessage[]) => {
    if (!user?.uid) return;
    await firestoreRepository.addOrReplaceDocument(FirestoreCollections.CONVERSATIONS, user.uid, {
      conversation: conv,
    });
  };

  const handleSend = async () => {
    if (!messageInput.trim()) return;

    setTimeout(() => setIsTyping(true), 450);

    const userMessage = { role: Roles.user, content: messageInput };
    setConversation((prev) => [...prev, userMessage]);
    const input = messageInput;
    setMessageInput('');

    const apiInput: ResponseInputItem = { role: Roles.user, content: input };

    let outputText;
    try {
      const response = await LlmService.callLlmApi(chatCompletionModel, systemInstruction, [...context, apiInput]);
      outputText = LlmService.extractOutputText(response);
    } catch (err) {
      console.error('LLM API error:', err);
      setIsTyping(false);
      return;
    }

    // Check if it's time to show the camera button
    if (isOnboarding && outputText.includes(SAVE_PREFERENCES)) {
      // Store preferences first, then show camera button
      await saveUserPreferences(outputText);
      // Remove the marker from the displayed message
      outputText = outputText.replace(
        SAVE_PREFERENCES,
        'Now take some pictures of your fridge, pantry, grocery receipt, or any food items you have and I will save them to the inventory list.'
      );
      setPreferencesCaptured(true);
      setShowCameraButton(true);
    }
    // Check if onboarding is complete (after camera photo is taken)
    else if (isOnboarding && outputText.includes(ONBOARDING_COMPLETE)) {
      router.replace('/(tabs)');
    }

    const assistantMessage = { role: Roles.assistant, content: outputText };
    setContext((prev) => [...prev, apiInput, assistantMessage]);
    setConversation((prev) => [...prev, assistantMessage]);
    setIsTyping(false);
  };

  const saveUserPreferences = async (preferencesText: string) => {
    if (!user?.uid) return;

    const assistantPrompt = {
      role: Roles.developer,
      content: `Create a JSON of the user's preferences from: '''${preferencesText}'''`,
    };

    const summaryResponse = await LlmService.callLlmApi(
      chatCompletionModel,
      'Respond ONLY with a raw JSON object.',
      [assistantPrompt],
      0.1,
      dietPreferencesSchema,
      'preferences'
    );

    try {
      const outputText = LlmService.extractOutputText(summaryResponse);
      const dietPreferences: DietPreferences = JSON.parse(outputText);

      console.log('dietPreferences', dietPreferences);
      await firestoreRepository.addOrReplaceDocument(FirestoreCollections.DIET_PREFERENCES, user.uid, dietPreferences);
    } catch (error) {
      console.error('Error saving preferences:', error);
    }
  };

  return {
    user,
    messageInput,
    setMessageInput,
    conversation,
    isTyping,
    handleSend,
    handleCameraPress,
    showCameraButton,
  };
}
