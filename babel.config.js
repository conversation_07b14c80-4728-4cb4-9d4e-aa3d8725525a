module.exports = function (api) {
  api.cache(true);
  return {
    presets: ['babel-preset-expo'],
    plugins: [
      ['babel-plugin-dotenv-import', {
        moduleName: '@env',
        path: '.env',
        safe: false,
        allowUndefined: true,
        blacklist: null,
        whitelist: ['OPENAI_API_KEY', 'UNSPLASH_ACCESS_KEY'],
      }],
      ["transform-inline-environment-variables", {
        include: [
          "NODE_ENV",
          "USE_MOCK_RECIPES"
        ]
      }]
    ]
  };
};
