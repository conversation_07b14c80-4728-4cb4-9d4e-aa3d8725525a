import React, { createContext, useContext, useState, useEffect, ReactNode } from 'react';
import { GroceryListService } from '@/app/services/GroceryListService';
import { GroceryItem, Tag } from '@/app/components/types';
import { useAuth } from '@/app/contexts/AuthContext';

interface GroceryListContextType {
  groceryItemCount: number;
  isUserAuthenticated: boolean;
  updateGroceryItemCount: () => Promise<GroceryItem[]>;
  addItem: (itemName: string, tags?: Tag[]) => Promise<GroceryItem[]>;
  addItems: (itemNames: string[], tags?: Tag[]) => Promise<GroceryItem[]>;
  toggleItemChecked: (itemName: string) => Promise<GroceryItem[]>;
  removeCheckedItems: () => Promise<GroceryItem[]>;
  updateItemTags: (itemName: string, tags: Tag[]) => Promise<GroceryItem[]>;
}

const GroceryListContext = createContext<GroceryListContextType | undefined>(undefined);

export const useGroceryList = () => {
  const context = useContext(GroceryListContext);
  if (!context) {
    throw new Error('useGroceryList must be used within a GroceryListProvider');
  }
  return context;
};

interface GroceryListProviderProps {
  children: ReactNode;
}

export const GroceryListProvider: React.FC<GroceryListProviderProps> = ({ children }) => {
  const [groceryItemCount, setGroceryItemCount] = useState(0);
  const { isAuthenticated } = useAuth();

  // Update grocery list when authentication state changes
  useEffect(() => {
    if (isAuthenticated) {
      updateGroceryItemCount();
    } else {
      setGroceryItemCount(0);
    }
  }, [isAuthenticated]);

  // Function to update the grocery item count and return the list
  const updateGroceryItemCount = async (): Promise<GroceryItem[]> => {
    try {
      if (!isAuthenticated) {
        console.log('Skipping grocery list fetch - user not authenticated yet');
        return [];
      }

      const groceryList = await GroceryListService.getGroceryList();
      setGroceryItemCount(groceryList.length);
      return groceryList;
    } catch (error) {
      console.error('Error fetching grocery list count:', error);
      return [];
    }
  };

  // Wrapper for GroceryListService.addItem that also updates the count
  const addItem = async (itemName: string, tags: Tag[] = []) => {
    try {
      const updatedList = await GroceryListService.addItem(itemName, tags);
      setGroceryItemCount(updatedList.length);
      return updatedList;
    } catch (error) {
      console.error('Error adding item to grocery list:', error);
      throw error;
    }
  };

  // Wrapper for GroceryListService.addItems that also updates the count
  const addItems = async (itemNames: string[], tags: Tag[] = []) => {
    try {
      const updatedList = await GroceryListService.addItems(itemNames, tags);
      setGroceryItemCount(updatedList.length);
      return updatedList;
    } catch (error) {
      console.error('Error adding items to grocery list:', error);
      throw error;
    }
  };

  // Wrapper for GroceryListService.toggleItemChecked
  const toggleItemChecked = async (itemName: string) => {
    try {
      const updatedList = await GroceryListService.toggleItemChecked(itemName);
      // Count doesn't change when toggling checked status
      return updatedList;
    } catch (error) {
      console.error('Error toggling item checked status:', error);
      throw error;
    }
  };

  // Wrapper for GroceryListService.removeCheckedItems
  const removeCheckedItems = async () => {
    try {
      const updatedList = await GroceryListService.removeCheckedItems();
      setGroceryItemCount(updatedList.length);
      return updatedList;
    } catch (error) {
      console.error('Error removing checked items:', error);
      throw error;
    }
  };

  // Wrapper for GroceryListService.updateItemTags
  const updateItemTags = async (itemName: string, tags: Tag[]) => {
    try {
      const updatedList = await GroceryListService.updateItemTags(itemName, tags);
      // Count doesn't change when updating tags
      return updatedList;
    } catch (error) {
      console.error('Error updating item tags:', error);
      throw error;
    }
  };

  const value = {
    groceryItemCount,
    isUserAuthenticated: isAuthenticated,
    updateGroceryItemCount,
    addItem,
    addItems,
    toggleItemChecked,
    removeCheckedItems,
    updateItemTags,
  };

  return <GroceryListContext.Provider value={value}>{children}</GroceryListContext.Provider>;
};
