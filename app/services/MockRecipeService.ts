import { Recipe, MealType, MealTypeItem, RecipeVariant, InstructionType } from '@/app/components/types';

/**
 * MockRecipeService provides sample recipe data for development and testing.
 * This will eventually be replaced with a real service that fetches data from an API
 * or allows for user input of recipes.
 */
export class MockRecipeService {
    /**
     * Get a list of sample recipes
     */
    static getRecipes(): Recipe[] {
        return [
            {
                id: 'pancakes',
                title: 'Pancakes',
                timeInMinutes: 15,
                calories: 420,
                imageUrl: 'https://images.unsplash.com/photo-1567620905732-2d1ec7ab7445?ixlib=rb-4.0.3&ixid=M3wxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8fA%3D%3D&auto=format&fit=crop&w=1080&q=80',
                ingredients: [
                    { name: '1 cup all-purpose flour', available: false },
                    { name: '2 tablespoons sugar', available: false },
                    { name: '1 teaspoon baking powder', available: true },
                    { name: '1/2 teaspoon salt', available: false },
                    { name: '1 egg', available: false },
                    { name: '1 cup milk', available: false },
                    { name: '2 tablespoons melted butter', available: false },
                ],
                instructions: {
                    [InstructionType.HIGH_LEVEL]: `Mix dry ingredients. Mix wet ingredients. Combine both. Cook on griddle until bubbles form, then flip. Serve with toppings.`,
                    [InstructionType.DETAILED]: `In a large bowl, whisk together the flour, sugar, baking powder, and salt.
In another bowl, beat the egg, then add milk and melted butter.
Pour the wet ingredients into the dry ingredients and stir just until combined (the batter should be slightly lumpy).
Heat a lightly oiled griddle or frying pan over medium-high heat. Pour about 1/4 cup of batter onto the griddle for each pancake.
Cook until bubbles form on the surface, then flip and cook until golden brown on both sides.
Serve hot with maple syrup, fresh berries, or your favorite toppings.`,
                    [InstructionType.TEACH_MODE]: `Let's make perfect pancakes step by step:

1. Start by gathering all your ingredients and measuring them precisely. Baking is a science, and accurate measurements are key to fluffy pancakes.

2. In a large bowl, whisk together 1 cup all-purpose flour, 2 tablespoons sugar, 1 teaspoon baking powder, and 1/2 teaspoon salt. Whisking aerates the dry ingredients and ensures even distribution of the leavening agents.

3. In a separate bowl, crack 1 egg and beat it lightly. The egg provides structure and richness to the pancakes.

4. Add 1 cup of milk to the beaten egg. Whole milk works best for richness, but any milk will work.

5. Melt 2 tablespoons of butter and let it cool slightly before adding to the milk mixture. The slight cooling prevents the butter from cooking the egg when they mix.

6. Pour the wet ingredients into the dry ingredients. Using a spatula or wooden spoon, gently fold the mixtures together with broad strokes. Mix only until combined - you should still see small lumps. Overmixing develops gluten, which makes pancakes tough instead of fluffy.

7. Let the batter rest for 5 minutes. This allows the flour to fully hydrate and the leavening agents to start working.

8. Heat a griddle or non-stick frying pan over medium heat. To test if it's ready, sprinkle a few drops of water on the surface - they should dance and evaporate quickly.

9. Lightly oil the cooking surface. Use a neutral oil or butter, applying just enough to coat the surface.

10. Pour about 1/4 cup of batter onto the griddle for each pancake. This amount makes a perfect 4-inch pancake.

11. Watch for bubbles to form on the surface of the pancake - this takes about 2-3 minutes. When the bubbles begin to pop and leave holes that don't fill in with batter, it's time to flip.

12. Flip the pancake with a thin spatula, using a quick, confident motion. Cook the second side for about 1-2 minutes until golden brown.

13. Transfer to a warm plate. You can keep pancakes warm in a 200°F oven while cooking the remaining batter.

14. Serve immediately with your favorite toppings. Classic maple syrup is traditional, but fresh berries, whipped cream, or even a sprinkle of powdered sugar are delicious alternatives.

Enjoy your homemade pancakes!`
                },
                mealType: MealType.BREAKFAST
            },
            {
                id: 'avocado-toast',
                title: 'Avocado Toast',
                timeInMinutes: 10,
                calories: 280,
                imageUrl: 'https://images.unsplash.com/photo-1588137378633-dea1336ce1e2?ixlib=rb-4.0.3&ixid=M3wxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8fA%3D%3D&auto=format&fit=crop&w=1080&q=80',
                ingredients: [
                    { name: '2 slices whole grain bread', available: false },
                    { name: '1 ripe avocado', available: true },
                    { name: '2 eggs', available: false },
                    { name: 'Salt and pepper to taste', available: false },
                    { name: 'Red pepper flakes', available: false },
                    { name: 'Extra virgin olive oil', available: false },
                    { name: 'Fresh lime juice', available: false },
                ],
                instructions: {
                    [InstructionType.HIGH_LEVEL]: `Toast bread. Mash avocado with lime, salt, and pepper. Spread on toast. Add toppings.`,
                    [InstructionType.DETAILED]: `Toast the bread slices until golden brown.
Cut the avocado in half, remove the pit, and mash the flesh in a bowl.
Add a squeeze of lime juice, salt, and pepper to the mashed avocado.
Spread the mashed avocado evenly on the toasted bread.
Optional: Top with poached or fried eggs.
Sprinkle with red pepper flakes and drizzle with olive oil.
Serve immediately while the toast is still warm.`,
                    [InstructionType.TEACH_MODE]: `Let's make perfect avocado toast step by step:

1. Start with good quality bread - whole grain or sourdough works best for a sturdy base and nutritional value.

2. Slice the bread to your preferred thickness (about 1/2 inch is ideal) and toast until golden brown and crisp. Toasting is crucial as it creates a barrier that prevents the bread from getting soggy from the avocado.

3. While the bread is toasting, select a ripe avocado. A ripe avocado should yield slightly to gentle pressure but not feel mushy. The small stem at the top should come off easily, revealing green underneath (brown indicates overripeness).

4. Cut the avocado in half lengthwise, rotating around the pit. Twist the halves to separate them.

5. Remove the pit safely by gently tapping a sharp knife into it, then twisting to release. Alternatively, scoop it out with a spoon.

6. Using a spoon, scoop the avocado flesh into a bowl. For chunky avocado toast, use a fork to mash it coarsely. For smoother texture, mash more thoroughly.

7. Season the avocado immediately with a squeeze of fresh lime juice (about 1 teaspoon), which adds flavor and prevents browning. Add 1/4 teaspoon salt and freshly ground black pepper to taste.

8. Spread the seasoned avocado evenly over the toasted bread, creating a layer about 1/4 to 1/2 inch thick.

9. For added protein and richness, top with a poached or fried egg. To poach an egg perfectly, create a gentle whirlpool in simmering water with a splash of vinegar, then slide the egg in and cook for 3-4 minutes.

10. Finish with a sprinkle of red pepper flakes for heat, a light drizzle of high-quality extra virgin olive oil for richness, and a final pinch of flaky sea salt for texture and flavor bursts.

11. Optional garnishes include thinly sliced radishes for crunch, microgreens for freshness, or crumbled feta cheese for tanginess.

12. Serve immediately while the toast is still warm and the avocado is fresh.

Enjoy your perfectly crafted avocado toast!`
                },
                mealType: MealType.BREAKFAST
            }
        ];
    }

    /**
     * Get meal type categories for filtering recipes
     */
    static getMealTypes(): MealTypeItem[] {
        return [
            { title: MealType.BREAKFAST, icon: 'egg-fried' },
            { title: MealType.LUNCH, icon: 'rice' },
            { title: MealType.DINNER, icon: 'pot-steam' },
        ];
    }

    /**
     * Get available recipe variants
     */
    static getRecipeVariants(): RecipeVariant[] {
        return ['Basic', 'Fancy', 'Chef curated'];
    }

    /**
     * Get available instruction types
     */
    static getInstructionTypes(): InstructionType[] {
        return [InstructionType.HIGH_LEVEL, InstructionType.DETAILED, InstructionType.TEACH_MODE];
    }
}

export default MockRecipeService;