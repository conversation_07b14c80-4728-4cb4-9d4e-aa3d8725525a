import { StyleSheet, Platform } from 'react-native';
import { getThemeColors } from '@/app/styles/Theme';

// Create a function to export styles that can be used with the current theme
const createStyles = (theme: 'light' | 'dark') => {
    const colors = getThemeColors(theme);

    return StyleSheet.create({
      container: {
        flex: 1,
        backgroundColor: colors.background,
      },
      safeArea: {
        flex: 1,
        paddingTop: Platform.OS === 'ios' ? 50 : 30, // Add padding for status bar
        paddingHorizontal: 16,
      },
      chatList: {
        flex: 1,
        width: '100%',
      },
      chatListContent: {
        paddingTop: 10,
        paddingBottom: 100, // Add extra padding at the bottom to ensure messages aren't hidden behind input
      },
      chatListItem: {
        paddingHorizontal: 4,
        paddingVertical: 4,
      },
      conversationUser: {
        fontSize: 16,
        padding: 14,
        marginVertical: 4,
        maxWidth: '80%',
        borderRadius: 20,
        borderBottomRightRadius: 4,
        backgroundColor: colors.userBubble,
        color: colors.userBubbleText,
        overflow: 'hidden',
        lineHeight: 22,
      },
      conversationBot: {
        fontSize: 16,
        padding: 14,
        marginVertical: 4,
        maxWidth: '85%',
        borderRadius: 20,
        borderBottomLeftRadius: 4,
        backgroundColor: colors.botBubble,
        color: colors.botBubbleText,
        overflow: 'hidden',
        lineHeight: 22,
      },
      typingIndicator: {
        fontStyle: 'italic',
        color: colors.statusText,
        paddingHorizontal: 10,
        paddingVertical: 2,
        marginLeft: 10,
      },
      keyboardAvoidViewWithTab: {
        marginBottom: 80,
      },
      keyboardAvoidView: {
        marginBottom: 30,
      },
      inputContainer: {
        flexDirection: 'row',
        alignItems: 'center',
        paddingVertical: 12,
        paddingHorizontal: 16,
        width: '100%',
      },
      chatInput: {
        flex: 1,
        borderRadius: 24,
        backgroundColor: colors.inputBackground,
        minHeight: 44,
        maxHeight: 100,
        paddingHorizontal: 16,
        paddingVertical: 10,
        paddingRight: 45,
        fontSize: 16,
        color: colors.inputText,
      },
      sendButton: {
        position: 'absolute',
        right: 24,
        bottom: 18,
        width: 32,
        height: 32,
        borderRadius: 16,
        backgroundColor: colors.accent,
        justifyContent: 'center',
        alignItems: 'center',
      },
      sendButtonText: {
        color: colors.accentText,
        fontSize: 16,
        fontWeight: 'bold',
      },
      optionsContainer: {
        flexDirection: 'row',
        flexWrap: 'wrap',
        marginTop: 8,
        marginBottom: 16,
        marginLeft: 10,
        gap: 8,
      },
      optionButton: {
        paddingVertical: 10,
        paddingHorizontal: 16,
        borderRadius: 20,
        backgroundColor: colors.selectionBackground,
        marginRight: 8,
        marginBottom: 8,
      },
      optionButtonSelected: {
        backgroundColor: colors.selectionActiveBackground,
      },
      optionButtonText: {
        fontSize: 16,
        color: colors.selectionText,
      },
      optionButtonTextSelected: {
        color: colors.selectionActiveText,
      },
      timeHeader: {
        alignItems: 'center',
        marginVertical: 10,
      },
      timeText: {
        fontSize: 12,
        color: colors.statusText,
      },
      buttonOnlyMessageContainer: {
        justifyContent: 'center',
      },
      takePictureButton: {
        backgroundColor: colors.accent,
        padding: 15,
        borderRadius: 20,
        marginVertical: 20,
        minWidth: 200,
        alignItems: 'center',
      },
      takePictureButtonText: {
        color: colors.accentText,
        fontWeight: 'bold',
        fontSize: 16,
      },
      messageSpacing: {
        margin: 15,
      },
    });
};

export default createStyles;
