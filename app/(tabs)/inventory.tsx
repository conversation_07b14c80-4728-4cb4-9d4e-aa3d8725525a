import React, { useEffect, useState } from 'react';
import { View, ScrollView, Text, useColorScheme } from 'react-native';
import { Appbar, Badge } from 'react-native-paper';
import { KeyboardAwareScrollView } from 'react-native-keyboard-aware-scroll-view';
import { useRouter } from 'expo-router';
import { getThemeColors } from '@/app/styles/Theme';
import createInventoryStyles from '@/app/styles/InventoryStyles';
import { InventoryItem, InventoryCategory } from '@/app/components/types';
import { InventoryService } from '@/app/services/InventoryService';
import InventoryCategoryComponent from '@/app/components/InventoryCategory';
import LoadingAnimation from '@/app/components/LoadingAnimation';
import { Colors } from '@/constants/Colors';
import ShoppingCart from '../../assets/images/icons/shopping-cart.svg';
import Camerta from '@/assets/images/icons/camera.svg';
import { useGroceryList } from '@/app/contexts/GroceryListContext';

export default function InventoryTab() {
  const colorScheme = useColorScheme() || 'light';
  const router = useRouter();
  const colors = getThemeColors(colorScheme as 'light' | 'dark');
  const styles = createInventoryStyles(colors);

  const [loading, setLoading] = useState(false);
  const [inventoryItems, setInventoryItems] = useState<InventoryItem[]>([]);
  const [categorizedInventory, setCategorizedInventory] = useState<InventoryCategory[]>([]);
  const { groceryItemCount } = useGroceryList();

  // Load inventory data
  useEffect(() => {
    loadInventory();
  }, []);

  // Load inventory from Firestore
  const loadInventory = async () => {
    try {
      setLoading(true);
      const items = await InventoryService.getUserInventory();
      setInventoryItems(items);

      if (items.length > 0) {
        const categories = await InventoryService.categorizeInventory(items);
        setCategorizedInventory(categories);
      } else {
        setCategorizedInventory([]);
      }
    } catch (error) {
      console.error('Error loading inventory:', error);
    } finally {
      setLoading(false);
    }
  };

  // Update local inventory state
  const updateLocalInventory = (updatedItems: InventoryItem[]) => {
    setInventoryItems(updatedItems);

    // Update categorized inventory
    if (updatedItems.length > 0) {
      let updatedCategories = [...categorizedInventory];

      // First, update existing items and remove deleted ones
      updatedCategories = updatedCategories
        .map((category) => {
          const updatedCategoryItems = category.items
            .map((item: InventoryItem) => {
              const updatedItem = updatedItems.find((i) => i.name === item.name);
              return updatedItem || item;
            })
            .filter((item: InventoryItem) => {
              // Keep only items that still exist in the updated inventory
              return updatedItems.some((i) => i.name === item.name);
            });

          return {
            ...category,
            items: updatedCategoryItems,
          };
        })
        .filter((category) => category.items.length > 0); // Remove empty categories

      // Find new items that need to be added to existing categories
      const existingItemNames = updatedCategories.flatMap((category) =>
        category.items.map((item: InventoryItem) => item.name)
      );

      const newItems = updatedItems.filter((item) => !existingItemNames.includes(item.name));

      // Add new items to appropriate categories
      if (newItems.length > 0) {
        for (const newItem of newItems) {
          // Find the category for this item
          const categoryName = newItem.category || 'Other';
          let categoryIndex = updatedCategories.findIndex((c) => c.name === categoryName);

          if (categoryIndex >= 0) {
            // Add to existing category
            updatedCategories[categoryIndex].items.push(newItem);
          } else {
            // Create a new category
            const emoji = getCategoryEmoji(categoryName);
            updatedCategories.push({
              name: categoryName,
              emoji: emoji,
              items: [newItem],
            });
          }
        }
      }

      setCategorizedInventory(updatedCategories);
    } else {
      setCategorizedInventory([]);
    }
  };

  // Helper function to get emoji for a category
  const getCategoryEmoji = (categoryName: string): string => {
    const categoryEmojis: Record<string, string> = {
      Fruits: '🍓',
      Vegetables: '🥦',
      Meat: '🥩',
      'Grains & Cereals': '🌾',
      'Dairy & Alternatives': '🥛',
      'Fats & Oils': '🫒',
      'Sweets & Snacks': '🍪',
      'Condiments & Spices': '🧂',
      Drinks: '🥤',
      Other: '🍥',
    };

    return categoryEmojis[categoryName] || '🍥'; // Default to 'Other' emoji
  };

  // Handle incrementing item quantity
  const handleIncrement = async (itemName: string) => {
    try {
      const item = inventoryItems.find((i: InventoryItem) => i.name === itemName);
      if (item) {
        // Update local state immediately
        const updatedItems = inventoryItems.map((i: InventoryItem) => {
          if (i.name === itemName) {
            return { ...i, quantity: i.quantity + 1 };
          }
          return i;
        });

        // Update UI
        updateLocalInventory(updatedItems);

        // Update backend in the background
        InventoryService.updateItemQuantity(itemName, item.quantity + 1).catch((error) => {
          console.error('Error incrementing item:', error);
          // Revert to previous state on error
          loadInventory();
        });
      }
    } catch (error) {
      console.error('Error incrementing item:', error);
    }
  };

  // Handle decrementing item quantity
  const handleDecrement = async (itemName: string) => {
    try {
      const item = inventoryItems.find((i: InventoryItem) => i.name === itemName);
      if (item) {
        if (item.quantity <= 1) {
          // If quantity is 1 or less, remove the item
          handleRemove(itemName);
        } else {
          // Update local state immediately
          const updatedItems = inventoryItems.map((i: InventoryItem) => {
            if (i.name === itemName) {
              return { ...i, quantity: i.quantity - 1 };
            }
            return i;
          });

          // Update UI
          updateLocalInventory(updatedItems);

          // Update backend in the background
          InventoryService.updateItemQuantity(itemName, item.quantity - 1).catch((error) => {
            console.error('Error decrementing item:', error);
            // Revert to previous state on error
            loadInventory();
          });
        }
      }
    } catch (error) {
      console.error('Error decrementing item:', error);
    }
  };

  // Handle removing an item
  const handleRemove = async (itemName: string) => {
    try {
      // Update local state immediately
      const updatedItems = inventoryItems.filter((i: InventoryItem) => i.name !== itemName);

      // Update UI
      updateLocalInventory(updatedItems);

      // Update backend in the background
      InventoryService.removeItem(itemName).catch((error) => {
        console.error('Error removing item:', error);
        // Revert to previous state on error
        loadInventory();
      });
    } catch (error) {
      console.error('Error removing item:', error);
    }
  };

  // Handle adding a new item
  const handleAddItem = async (itemName: string, categoryName: string) => {
    try {
      // Create new item with default quantity of 1
      const newItem: InventoryItem = {
        name: itemName,
        quantity: 1,
        addedAt: Date.now(),
      };

      // Update backend in the background
      InventoryService.addOrUpdateItem(newItem).catch((error) => {
        console.error('Error adding item:', error);
        // Revert to previous state on error
        loadInventory();
      });

      // Update local state immediately
      const itemWithCategory = { ...newItem, category: categoryName };
      const updatedItems = [...inventoryItems, itemWithCategory];
      updateLocalInventory(updatedItems);
    } catch (error) {
      console.error('Error adding item:', error);
    }
  };

  // Render empty state
  const renderEmptyState = () => {
    // Create default categories for empty state
    const defaultCategories: InventoryCategory[] = [
      { name: 'Fruits', emoji: '🍓', items: [] },
      { name: 'Vegetables', emoji: '🥦', items: [] },
      { name: 'Proteins', emoji: '🥩', items: [] },
      { name: 'Grains & Cereals', emoji: '🌾', items: [] },
      { name: 'Dairy & Alternatives', emoji: '🥛', items: [] },
    ];

    return (
      <ScrollView style={styles.content}>
        <View style={styles.emptyStateContainer}>
          <Text style={styles.emptyStateTitle}>Your inventory is empty</Text>
          <Text style={styles.emptyStateText}>
            Add items to your inventory by taking a photo of your fridge or pantry or manually adding items below.
          </Text>
        </View>

        {defaultCategories.map((category, index) => (
          <InventoryCategoryComponent
            key={`${category.name}-${index}`}
            category={category}
            onIncrement={handleIncrement}
            onDecrement={handleDecrement}
            onRemove={handleRemove}
            onAddItem={handleAddItem}
          />
        ))}
      </ScrollView>
    );
  };

  return (
    <View style={styles.container}>
      <Appbar.Header style={styles.header}>
        <Appbar.Action
          icon={() => <Camerta fill={Colors[colorScheme].tint} />}
          onPress={() => router.push('/camera')}
        />
        <Appbar.Content title='' style={styles.title} />
        <View>
          <Appbar.Action
            icon={() => <ShoppingCart fill={Colors[colorScheme].tint} />}
            onPress={() => {
              router.push('/grocery-list');
            }}
          />
          {groceryItemCount > 0 && (
            <Badge
              visible={true}
              size={20}
              style={{
                position: 'absolute',
                top: 5,
                right: 5,
                backgroundColor: colors.accent,
              }}
            >
              {groceryItemCount}
            </Badge>
          )}
        </View>
      </Appbar.Header>

      {loading ? (
        <LoadingAnimation
          source={require('../../assets/images/gifs/bounce-veggie.gif')}
          message='Sorting your snacks and veggies… one carrot at a time 🥕'
        />
      ) : inventoryItems.length === 0 ? (
        renderEmptyState()
      ) : (
        <KeyboardAwareScrollView
          enableOnAndroid={true}
          enableAutomaticScroll={true}
          extraScrollHeight={100}
          keyboardShouldPersistTaps='always'
        >
          <ScrollView style={styles.content} keyboardShouldPersistTaps='always'>
            {categorizedInventory.map((category: InventoryCategory, index: number) => (
              <InventoryCategoryComponent
                key={`${category.name}-${index}`}
                category={category}
                onIncrement={handleIncrement}
                onDecrement={handleDecrement}
                onRemove={handleRemove}
                onAddItem={handleAddItem}
              />
            ))}
          </ScrollView>
        </KeyboardAwareScrollView>
      )}
    </View>
  );
}
