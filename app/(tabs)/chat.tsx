import { useChatExperience } from '@/hooks/useChatExperience';
import ChatUI from '@/app/components/ChatUI';
import { ChatMode } from '@/constants/Types';

export default function ChatScreen() {
  const {
    conversation,
    messageInput,
    setMessageInput,
    isTyping,
    handleSend,
    handleCameraPress,
  } = useChatExperience(ChatMode.chat);

  return (
    <ChatUI
      messages={conversation}
      inputValue={messageInput}
      onInputChange={setMessageInput}
      onSendMessage={handleSend}
      onTakePicture={handleCameraPress}
      showTypingIndicator={isTyping}
      placeholder="Type a message..."
    />
  );
}