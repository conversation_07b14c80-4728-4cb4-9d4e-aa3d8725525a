import React, { useRef, useEffect } from 'react';
import {
  View,
  Text,
  FlatList,
  TextInput,
  TouchableOpacity,
  KeyboardAvoidingView,
  Keyboard,
  Platform,
  TouchableWithoutFeedback,
  useColorScheme,
} from 'react-native';
import createChatStyles from '@/app/styles/ChatStyles';
import { getThemeColors } from '@/app/styles/Theme';
import { ChatMessage, ChatMode, Roles, SHOW_TAKE_PICTURE_BUTTON } from '@/constants/Types';

interface ChatUIProps {
  messages: ChatMessage[];
  inputValue: string;
  onInputChange: (text: string) => void;
  onSendMessage: () => void;
  onTakePicture?: () => void;
  showTypingIndicator?: boolean;
  placeholder?: string;
  chatMode?: ChatMode;
  containerStyle?: object;
}

const ChatUI: React.FC<ChatUIProps> = ({
  messages,
  inputValue,
  onInputChange,
  onSendMessage,
  onTakePicture,
  showTypingIndicator = false,
  placeholder = 'Type a message...',
  chatMode = ChatMode.chat,
  containerStyle = {},
}) => {
  const colorScheme = useColorScheme() || 'light';
  const styles = createChatStyles(colorScheme as 'light' | 'dark');
  const colors = getThemeColors(colorScheme as 'light' | 'dark');
  const flatListRef = useRef<FlatList>(null);

  // Scroll to the bottom whenever messages update
  useEffect(() => {
    if (flatListRef.current && messages.length > 0) {
      // Use a slight delay if the last message is not from the user
      const delay = messages[messages.length - 1]?.role === Roles.user ? 0 : 20;
      setTimeout(() => {
        flatListRef.current?.scrollToEnd({ animated: true });
      }, delay);
    }
  }, [messages]);

  const renderMessage = ({ item, index }: { item: ChatMessage; index: number }) => {
    const isUser = item.role === Roles.user;
    const isDeveloper = item.role === Roles.developer;

    // Check if this is the special button-only message
    const isButtonOnlyMessage = isDeveloper && item.content === SHOW_TAKE_PICTURE_BUTTON;

    // For button-only messages, only render the button
    if (isButtonOnlyMessage && onTakePicture) {
      return (
        <View style={[styles.chatListItem, styles.buttonOnlyMessageContainer]}>
          <TouchableOpacity style={styles.takePictureButton} onPress={onTakePicture}>
            <Text style={styles.takePictureButtonText}> Take a picture </Text>
          </TouchableOpacity>
        </View>
      );
    }
    return (
      <View key={index}>
        <View style={styles.chatListItem}>
          <Text
            style={[
              isUser ? styles.conversationUser : styles.conversationBot,
              { alignSelf: isUser ? 'flex-end' : 'flex-start' },
            ]}
          >
            {item.content}
          </Text>
        </View>

        {/* If this is the last message, show typing indicator if active */}
        {index === messages.length - 1 &&
          (showTypingIndicator ? (
            <Text style={styles.typingIndicator}> Typing...</Text>
          ) : (
            <View style={styles.messageSpacing} />
          ))}
      </View>
    );
  };

  return (
    <View style={[styles.container, containerStyle]}>
      <View style={styles.safeArea}>
        <FlatList
          ref={flatListRef}
          data={messages}
          keyExtractor={(_, index) => index.toString()}
          renderItem={renderMessage}
          onContentSizeChange={() => flatListRef.current?.scrollToEnd({ animated: true })}
          onLayout={() => flatListRef.current?.scrollToEnd({ animated: true })}
          style={styles.chatList}
          contentContainerStyle={styles.chatListContent}
          showsVerticalScrollIndicator={false}
        />
      </View>

      <KeyboardAvoidingView
        behavior={Platform.OS === 'ios' ? 'padding' : 'height'}
        style={chatMode == ChatMode.chat ? styles.keyboardAvoidViewWithTab : styles.keyboardAvoidView}
      >
        <TouchableWithoutFeedback onPress={Keyboard.dismiss}>
          <View style={styles.inputContainer}>
            <TextInput
              style={styles.chatInput}
              multiline={true}
              onChangeText={onInputChange}
              value={inputValue}
              placeholder={placeholder}
              placeholderTextColor={colors.inputPlaceholder}
            />
            <TouchableOpacity style={styles.sendButton} onPress={onSendMessage}>
              <Text style={styles.sendButtonText}>→</Text>
            </TouchableOpacity>
          </View>
        </TouchableWithoutFeedback>
      </KeyboardAvoidingView>
    </View>
  );
};

export default ChatUI;