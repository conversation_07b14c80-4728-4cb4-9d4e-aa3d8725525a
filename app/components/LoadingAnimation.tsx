import React from 'react';
import { View, Text, Image, StyleSheet, useColorScheme, ImageSourcePropType } from 'react-native';
import { getThemeColors } from '@/app/styles/Theme';
import { ThemeColors } from '@/app/styles/Theme';

interface LoadingAnimationProps {
  /**
   * The URI of the loading animation GIF
   */
  source: ImageSourcePropType;

  /**
   * The message to display below the loading animation
   */
  message: string;
}

/**
 * A reusable loading animation component that displays a centered GIF and message
 */
const LoadingAnimation: React.FC<LoadingAnimationProps> = ({ source, message }) => {
  const colorScheme = useColorScheme() || 'light';
  const colors = getThemeColors(colorScheme as 'light' | 'dark');
  const styles = createLoadingStyles(colors);

  return (
    <View style={styles.loadingContainer}>
      <Image source={source} style={styles.loadingGif} />
      <Text style={styles.loadingMessage}>{message}</Text>
    </View>
  );
};

/**
 * Create styles for the loading animation based on the current theme
 */
const createLoadingStyles = (colors: ThemeColors) => {
  return StyleSheet.create({
    loadingContainer: {
      flex: 1,
      justifyContent: 'center',
      alignItems: 'center',
      paddingBottom: 0, // Removed paddingBottom for better vertical centering
    },
    loadingGif: {
      width: 200,
      height: 200,
      marginBottom: 20,
    },
    loadingMessage: {
      fontSize: 18,
      fontWeight: '300',
      color: colors.text,
      textAlign: 'center',
      marginVertical: 16,
      marginHorizontal: 30,
    },
  });
};

export default LoadingAnimation;
