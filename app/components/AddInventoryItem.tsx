import React, { useState } from 'react';
import { View, TextInput, StyleSheet, useColorScheme } from 'react-native';
import { Button } from 'react-native-paper';
import { getThemeColors } from '@/app/styles/Theme';

interface AddInventoryItemProps {
  categoryName: string;
  onAddItem: (itemName: string, categoryName: string) => void;
  onCancel: () => void;
}

const AddInventoryItem: React.FC<AddInventoryItemProps> = ({ categoryName, onAddItem, onCancel }) => {
  const [itemName, setItemName] = useState('');
  const colorScheme = useColorScheme() || 'light';
  const colors = getThemeColors(colorScheme as 'light' | 'dark');

  const handleAddItem = () => {
    if (itemName.trim()) {
      onAddItem(itemName.trim(), categoryName);
      setItemName('');
    }
  };

  return (
    <View style={styles.container}>
      <TextInput
        style={[styles.input, { color: colors.text, borderColor: colors.divider }]}
        placeholder='Enter ingredient name'
        placeholderTextColor={colors.textSecondary}
        value={itemName}
        onChangeText={setItemName}
        autoFocus
      />
      <View style={styles.buttonContainer}>
        <Button mode='text' onPress={onCancel} textColor={colors.textSecondary} style={styles.button}>
          Cancel
        </Button>
        <Button
          mode='contained'
          onPress={handleAddItem}
          buttonColor={colors.accent}
          textColor={colors.accentText}
          style={styles.button}
          disabled={!itemName.trim()}
        >
          Add
        </Button>
      </View>
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    marginVertical: 16,
  },
  input: {
    height: 48,
    borderWidth: 1,
    borderRadius: 8,
    paddingHorizontal: 12,
    fontSize: 16,
    marginBottom: 8,
  },
  buttonContainer: {
    flexDirection: 'row',
    justifyContent: 'flex-end',
  },
  button: {
    marginLeft: 8,
  },
});

export default AddInventoryItem;
