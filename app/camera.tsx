import React, { useState } from 'react';
import { useRouter } from 'expo-router';
import LoadingAnimation from '@/app/components/LoadingAnimation';
import CaptureView from '@/app/components/CaptureView';
import { analyzeImagesAsync } from '@/app/services/analyzeImages';
import { InventoryService } from '@/app/services/InventoryService';

export default function CameraScreen() {
  const router = useRouter();
  const [isAnalyzing, setIsAnalyzing] = useState(false);

  const handlePhotosTaken = async (photos: string[]) => {
    setIsAnalyzing(true);
    try {
      const ingredients = await analyzeImagesAsync(photos);

      // Save ingredients to inventory
      for (const ingredient of ingredients) {
        await InventoryService.addOrUpdateItem({
          name: ingredient.name,
          quantity: 1, // Default quantity
        });
      }

      // Navigate back to inventory tab
      router.push('/(tabs)/inventory');
    } catch (error) {
      console.error('Error analyzing photos:', error);
      // TODO: Show error message to user
      router.back();
    } finally {
      setIsAnalyzing(false);
    }
  };

  if (isAnalyzing) {
    return (
      <LoadingAnimation
        source={require('../assets/images/gifs/bounce-veggie.gif')}
        message='Analyzing your food items...'
      />
    );
  }

  return <CaptureView onPhotosTaken={handlePhotosTaken} onClose={() => router.back()} />;
}