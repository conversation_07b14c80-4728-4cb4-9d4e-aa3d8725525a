import { DarkTheme, DefaultTheme, ThemeProvider } from '@react-navigation/native';
import { PaperProvider, MD3LightTheme, MD3DarkTheme } from 'react-native-paper';
import { useFonts } from 'expo-font';
import { Stack } from 'expo-router';
import * as SplashScreen from 'expo-splash-screen';
import { StatusBar } from 'expo-status-bar';
import { useEffect } from 'react';
import { useColorScheme } from 'react-native';
import 'react-native-reanimated';
import { GroceryListProvider } from '@/app/contexts/GroceryListContext';
import { AuthProvider } from '@/app/contexts/AuthContext';

// Prevent the splash screen from auto-hiding before asset loading is complete.
SplashScreen.preventAutoHideAsync();

export default function RootLayout() {
  const colorScheme = useColorScheme() || 'light';
  const [loaded] = useFonts({
    SpaceMono: require('../assets/fonts/SpaceMono-Regular.ttf'),
  });

  useEffect(() => {
    if (loaded) {
      SplashScreen.hideAsync();
    }
  }, [loaded]);

  if (!loaded) {
    return null;
  }

  // Define the themes
  const paperTheme = colorScheme === 'dark' ? MD3DarkTheme : MD3LightTheme;
  const navigationTheme = colorScheme === 'dark' ? DarkTheme : DefaultTheme;

  return (
    <PaperProvider theme={paperTheme}>
      <ThemeProvider value={navigationTheme}>
        <AuthProvider>
          <GroceryListProvider>
            <Stack>
              <Stack.Screen name='index' options={{ headerShown: false }} />
              <Stack.Screen name='(tabs)' options={{ headerShown: false, title: 'Home' }} />
              <Stack.Screen name='onboarding' options={{ headerShown: false }} />
              <Stack.Screen
                name='camera'
                options={{
                  title: '',
                  headerBackTitle: 'Back',
                }}
              />
              <Stack.Screen
                name='grocery-list'
                options={{
                  title: 'Grocery List',
                  headerShown: false,
                }}
              />
            </Stack>
            <StatusBar style='auto' />
          </GroceryListProvider>
        </AuthProvider>
      </ThemeProvider>
    </PaperProvider>
  );
}
