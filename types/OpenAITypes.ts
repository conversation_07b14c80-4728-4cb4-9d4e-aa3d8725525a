/**
 * Type definitions for OpenAI API response objects
 */

// Base types for common structures
export type OpenAIErrorObject = {
  message: string;
  type: string;
  param?: string;
  code?: string;
};

// Types for output content items
export type OutputTextItem = {
  type: 'output_text';
  text: string;
  annotations: any[];
};

export type OutputMessageContent = OutputTextItem;

export type OutputMessage = {
  type: 'message';
  id: string;
  status: 'completed' | 'failed' | 'in_progress' | 'incomplete';
  role: 'assistant';
  content: OutputMessageContent[];
};

export type FileSearchToolCall = {
  type: 'file_search_tool_call';
  // Add properties as needed
};

export type FunctionToolCall = {
  type: 'function_tool_call';
  // Add properties as needed
};

export type WebSearchToolCall = {
  type: 'web_search_tool_call';
  // Add properties as needed
};

export type ComputerToolCall = {
  type: 'computer_tool_call';
  // Add properties as needed
};

export type ReasoningItem = {
  type: 'reasoning';
  // Add properties as needed
};

export type OutputItem =
  | OutputMessage
  | FileSearchToolCall
  | FunctionToolCall
  | WebSearchToolCall
  | ComputerToolCall
  | ReasoningItem;

// Usage details
export type UsageDetails = {
  input_tokens: number;
  input_tokens_details: {
    cached_tokens: number;
  };
  output_tokens: number;
  output_tokens_details: {
    reasoning_tokens: number;
  };
  total_tokens: number;
};

// Incomplete details
export type IncompleteDetails = {
  reason: string;
  // Add other properties as needed
};

// Reasoning configuration
export type ReasoningConfig = {
  effort: string | null;
  generate_summary: boolean | null;
};

// Text format configuration
export type TextFormat = {
  format: {
    type: string;
  };
};

export enum TextFormatType {
  text = 'text',
  json = 'json_schema',
}

/**
 * Main OpenAI API Response object type
 */
export type OpenAIResponse = {
  id: string;
  object: string;
  created_at: number;
  status: 'completed' | 'failed' | 'in_progress' | 'incomplete';
  error: OpenAIErrorObject | null;
  incomplete_details: IncompleteDetails | null;
  instructions: string | null;
  max_output_tokens: number | null;
  model: string;
  output: OutputItem[];
  output_text?: string | null; // SDK-only convenience property
  parallel_tool_calls: boolean;
  previous_response_id: string | null;
  reasoning: ReasoningConfig | null;
  store?: boolean;
  temperature: number | null;
  text: TextFormat;
  tool_choice: string | object;
  tools: any[]; // Can be expanded with specific tool types
  top_p: number | null;
  truncation: string | null;
  usage: UsageDetails;
  user: string | null;
  metadata: Record<string, string>;
};
